# Gitee AI 抠图接口 Chat Completions 中转服务

本项目实现了一个 Node.js 中转服务，将 Gitee AI 抠图接口（/v1/images/mattings）包装为 Chat Completions 格式的 API，便于统一对接。

## 使用说明

### 1. 安装依赖

```bash
npm install
```

### 2. 启动服务

```bash
npm start
```

默认监听 3000 端口。

### 3. 调用方式

POST `/v1/chat/completions`

- Header:
  - `x-baseurl`: Gitee AI 基础 API 地址（如 https://ai.gitee.com）
  - `Authorization`: Gitee API Key
- Body（JSON）：

```json
{
  "model": "Yi-34B-Chat",
  "messages": [
    {
      "content": "<base64图片数据>",
      "role": "user"
    }
  ],
  "response_format": "b64_json"
}
```

### 4. 响应格式

返回内容为标准 chat completions 格式，图片结果在 `choices[0].message.content` 字段中。

```json
{
  "id": "matting-1718539260",
  "object": "text_completion",
  "created": 1718539260,
  "choices": [
    {
      "index": 0,
      "finish_reason": "stop",
      "message": {
        "role": "assistant",
        "content": "<图片URL或base64>"
      },
      "logprobs": null
    }
  ]
}
```

## 高并发说明

- 本服务基于 Express + Axios，异步非阻塞，支持高并发。
- 建议部署在支持 Node.js 的服务器上，并使用 pm2、docker 等方式守护。

## 错误处理

- 若请求参数有误或 Gitee API 返回错误，将返回 400/500 错误及详细信息。

---

如需定制更多功能，请联系开发者。
