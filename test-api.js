const axios = require('axios');

// 创建一个简单的测试图片的base64数据（1x1像素的PNG）
const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==';

const testData = {
  model: "RMBG-2.0",
  messages: [
    {
      role: "user",
      content: [
        {
          type: "image_url",
          image_url: {
            url: testImageBase64
          }
        }
      ]
    }
  ]
};

async function testAPI() {
  try {
    console.log('开始测试API...');
    
    const response = await axios.post('http://localhost:3000/v1/chat/completions', testData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer LCG4CJUOM1J8ZFRULOKVLSSLIUY7AZKTBOM054CD'
      },
      timeout: 30000
    });
    
    console.log('API测试成功！');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('错误信息:', error.message);
    }
  }
}

testAPI();
