const axios = require('axios');

// 创建一个简单的测试图片的base64数据（1x1像素的PNG）
const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==';

const testData = {
  model: "RMBG-2.0",
  messages: [
    {
      role: "user",
      content: [
        {
          type: "image_url",
          image_url: {
            url: testImageBase64
          }
        }
      ]
    }
  ],
  stream: true  // 启用流式响应
};

async function testAPI() {
  try {
    console.log('开始测试流式API...');

    const response = await axios.post('http://localhost:3000/v1/chat/completions', testData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer LCG4CJUOM1J8ZFRULOKVLSSLIUY7AZKTBOM054CD'
      },
      timeout: 30000,
      responseType: 'stream'  // 接收流式响应
    });

    console.log('API连接成功，开始接收流式数据...');
    console.log('响应状态:', response.status);

    let fullContent = '';

    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log('流式响应结束');
            return;
          }
          try {
            const parsed = JSON.parse(data);
            if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
              fullContent += parsed.choices[0].delta.content;
              console.log('接收到内容片段:', parsed.choices[0].delta.content);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    });

    response.data.on('end', () => {
      console.log('流式响应完成！');
      console.log('完整内容:', fullContent);
    });

  } catch (error) {
    console.error('API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('错误信息:', error.message);
    }
  }
}

testAPI();
