const express = require('express');
const axios = require('axios');
const FormData = require('form-data');

const app = express();
app.use(express.json({ limit: '10mb' }));

// 主中转接口
app.post('/v1/chat/completions', async (req, res) => {
  try {
    // 从请求头获取 baseurl 和 key
    const baseurl = 'https://ai.gitee.com';
    const apiKey = req.headers['authorization'] || '';
    // baseurl 固定为 Gitee 官方地址，无需校验

    // 解析请求体
    const { model, messages, response_format } = req.body;
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'messages 不能为空' });
    }
    // 日志：将请求体和 headers 写入 logs/request.log，并省略 base64 内容
    const fs = require('fs');
    const logDir = './logs';
    if (!fs.existsSync(logDir)) fs.mkdirSync(logDir);
    // 处理 body，省略 base64 字段
    const safeBody = JSON.parse(JSON.stringify(req.body));
    if (safeBody && safeBody.messages && Array.isArray(safeBody.messages)) {
      safeBody.messages.forEach(msg => {
        if (msg.content && Array.isArray(msg.content)) {
          msg.content.forEach(item => {
            if (item.type === 'image_url' && item.image_url && item.image_url.url) {
              if (typeof item.image_url.url === 'string' && item.image_url.url.startsWith('data:image/')) {
                item.image_url.url = '[base64 omitted]';
              }
            }
          });
        }
      });
    }
    const logData = `\n[${new Date().toISOString()}]\nHeaders: ${JSON.stringify(req.headers)}\nBody: ${JSON.stringify(safeBody)}\n`;
    fs.appendFileSync(logDir + '/request.log', logData);
    console.log(logData);

    // 新图片提取逻辑：取最后一条消息的最后一张图片
    const lastMessage = messages[messages.length - 1];
    let content = '';
    if (lastMessage && Array.isArray(lastMessage.content)) {
      // 过滤 type 为 'image_url' 的内容
      const imageContents = lastMessage.content.filter(item => item.type === 'image_url');
      if (imageContents.length > 0) {
        const lastImageContent = imageContents[imageContents.length - 1];
        content = lastImageContent && lastImageContent.image_url && lastImageContent.image_url.url || '';
      }
    }
    // 日志：打印 base64 内容前100字符和长度
    console.log('收到图片 base64 内容（前100字符）:', content.slice(0, 100));
    console.log('base64 长度:', content.length);
    if (!content) {
      return res.status(400).json({ error: 'content 不能为空，需 base64 图片数据' });
    }

    // 构造 multipart/form-data
    const form = new FormData();
    form.append('model', 'RMBG-2.0');
    form.append('response_format', 'url');  // 添加这个参数让Gitee返回URL
    // 将 base64 转为 Buffer 并以文件流方式上传
    const base64Data = content.replace(/^data:image\/\w+;base64,/, '');
    const imgBuffer = Buffer.from(base64Data, 'base64');
    form.append('image', imgBuffer, { filename: 'image.png', contentType: 'image/png' });

    // 转发请求到 Gitee 抠图接口
    console.log('正在调用 Gitee API...');
    const resp = await axios.post(
      `${baseurl}/v1/images/mattings`,
      form,
      {
        headers: {
          ...form.getHeaders(),
          Authorization: apiKey,
        },
        timeout: 20000,
      }
    );
    console.log('Gitee API 响应状态:', resp.status);
    console.log('Gitee API 响应数据:', JSON.stringify(resp.data, null, 2));

    // 构造 chat completions 格式响应
    const mattingData = resp.data && resp.data.data && resp.data.data[0] ? resp.data.data[0] : {};

    // 优先使用URL，如果没有URL则使用base64
    let imageResult = '';
    if (mattingData.url) {
      imageResult = mattingData.url;
      console.log('返回图片URL:', imageResult);
    } else if (mattingData.b64_json) {
      imageResult = `data:image/png;base64,${mattingData.b64_json}`;
      console.log('返回base64图片数据，长度:', mattingData.b64_json.length);
    } else {
      console.log('未找到图片数据');
    }

    // 检查是否需要流式响应
    const isStream = req.body.stream === true;

    if (isStream) {
      // 流式响应
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', '*');

      const streamId = `chatcmpl-${Date.now()}`;
      const created = Math.floor(Date.now() / 1000);

      // 发送开始chunk
      const startChunk = {
        id: streamId,
        object: 'chat.completion.chunk',
        created: created,
        model: 'RMBG-2.0',
        choices: [
          {
            index: 0,
            delta: {
              role: 'assistant',
              content: ''
            },
            logprobs: null,
            finish_reason: null
          }
        ]
      };
      res.write(`data: ${JSON.stringify(startChunk)}\n\n`);

      // 发送内容chunk - 使用Markdown格式显示图片
      const imageMarkdown = `![抠图结果](${imageResult})`;
      const contentChunk = {
        id: streamId,
        object: 'chat.completion.chunk',
        created: created,
        model: 'RMBG-2.0',
        choices: [
          {
            index: 0,
            delta: {
              content: imageMarkdown
            },
            logprobs: null,
            finish_reason: null
          }
        ]
      };
      res.write(`data: ${JSON.stringify(contentChunk)}\n\n`);

      // 发送结束chunk
      const endChunk = {
        id: streamId,
        object: 'chat.completion.chunk',
        created: created,
        model: 'RMBG-2.0',
        choices: [
          {
            index: 0,
            delta: {},
            logprobs: null,
            finish_reason: 'stop'
          }
        ]
      };
      res.write(`data: ${JSON.stringify(endChunk)}\n\n`);
      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      // 非流式响应 - 使用Markdown格式显示图片
      const imageMarkdown = `![抠图结果](${imageResult})`;
      const chatResp = {
        id: `matting-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: 'RMBG-2.0',
        choices: [
          {
            index: 0,
            finish_reason: 'stop',
            message: {
              role: 'assistant',
              content: imageMarkdown
            },
            logprobs: null,
          }
        ],
        usage: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }
      };
      res.json(chatResp);
    }
  } catch (err) {
    // 打印详细错误日志
    console.error('处理请求时发生错误:', err.message);
    if (err.response) {
      console.error('Gitee API 调用异常:');
      console.error('status:', err.response.status);
      console.error('headers:', err.response.headers);
      console.error('data:', err.response.data);
    }

    // 检查是否需要流式错误响应
    const isStream = req.body.stream === true;

    if (isStream) {
      // 流式错误响应
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', '*');

      const errorChunk = {
        id: `chatcmpl-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: 'RMBG-2.0',
        choices: [
          {
            index: 0,
            delta: {
              role: 'assistant',
              content: `抱歉，图片处理失败：${err.message}`
            },
            logprobs: null,
            finish_reason: 'stop'
          }
        ]
      };
      res.write(`data: ${JSON.stringify(errorChunk)}\n\n`);
      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      // 非流式错误响应
      if (err.response) {
        res.status(err.response.status || 500).json({
          error: 'Gitee API 调用失败',
          status: err.response.status,
          headers: err.response.headers,
          data: err.response.data,
          message: err.message
        });
      } else {
        res.status(500).json({ error: err.message });
      }
    }
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`中转服务已启动，端口: ${PORT}`);
});
