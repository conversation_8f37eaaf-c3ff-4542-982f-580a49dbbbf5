const express = require('express');
const axios = require('axios');
const FormData = require('form-data');

const app = express();
app.use(express.json({ limit: '10mb' }));

// 主中转接口
app.post('/v1/chat/completions', async (req, res) => {
  try {
    // 从请求头获取 baseurl 和 key
    const baseurl = 'https://ai.gitee.com';
    const apiKey = req.headers['authorization'] || '';
    // baseurl 固定为 Gitee 官方地址，无需校验

    // 解析请求体
    const { model, messages, response_format } = req.body;
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'messages 不能为空' });
    }
    // 日志：将请求体和 headers 写入 logs/request.log，并省略 base64 内容
    const fs = require('fs');
    const logDir = './logs';
    if (!fs.existsSync(logDir)) fs.mkdirSync(logDir);
    // 处理 body，省略 base64 字段
    const safeBody = JSON.parse(JSON.stringify(req.body));
    if (safeBody && safeBody.messages && Array.isArray(safeBody.messages)) {
      safeBody.messages.forEach(msg => {
        if (msg.content && Array.isArray(msg.content)) {
          msg.content.forEach(item => {
            if (item.type === 'image_url' && item.image_url && item.image_url.url) {
              if (typeof item.image_url.url === 'string' && item.image_url.url.startsWith('data:image/')) {
                item.image_url.url = '[base64 omitted]';
              }
            }
          });
        }
      });
    }
    const logData = `\n[${new Date().toISOString()}]\nHeaders: ${JSON.stringify(req.headers)}\nBody: ${JSON.stringify(safeBody)}\n`;
    fs.appendFileSync(logDir + '/request.log', logData);
    console.log(logData);

    // 新图片提取逻辑：取最后一条消息的最后一张图片
    const lastMessage = messages[messages.length - 1];
    let content = '';
    if (lastMessage && Array.isArray(lastMessage.content)) {
      // 过滤 type 为 'image_url' 的内容
      const imageContents = lastMessage.content.filter(item => item.type === 'image_url');
      if (imageContents.length > 0) {
        const lastImageContent = imageContents[imageContents.length - 1];
        content = lastImageContent && lastImageContent.image_url && lastImageContent.image_url.url || '';
      }
    }
    // 日志：打印 base64 内容前100字符和长度
    console.log('收到图片 base64 内容（前100字符）:', content.slice(0, 100));
    console.log('base64 长度:', content.length);
    if (!content) {
      return res.status(400).json({ error: 'content 不能为空，需 base64 图片数据' });
    }

    // 构造 multipart/form-data
    const form = new FormData();
    form.append('model', 'RMBG-2.0');
    // 将 base64 转为 Buffer 并以文件流方式上传
    const base64Data = content.replace(/^data:image\/\w+;base64,/, '');
    const imgBuffer = Buffer.from(base64Data, 'base64');
    form.append('image', imgBuffer, { filename: 'image.png', contentType: 'image/png' });

    // 转发请求到 Gitee 抠图接口
    console.log('正在调用 Gitee API...');
    const resp = await axios.post(
      `${baseurl}/v1/images/mattings`,
      form,
      {
        headers: {
          ...form.getHeaders(),
          Authorization: apiKey,
        },
        timeout: 20000,
      }
    );
    console.log('Gitee API 响应状态:', resp.status);
    console.log('Gitee API 响应数据:', JSON.stringify(resp.data, null, 2));

    // 构造 chat completions 格式响应
    const mattingData = resp.data && resp.data.data && resp.data.data[0] ? resp.data.data[0] : {};
    const b64Image = mattingData.b64_json || '';

    // 如果有base64图片数据，转换为data URL格式
    const imageDataUrl = b64Image ? `data:image/png;base64,${b64Image}` : '';

    const chatResp = {
      id: `matting-${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: 'RMBG-2.0',
      choices: [
        {
          index: 0,
          finish_reason: 'stop',
          message: {
            role: 'assistant',
            content: imageDataUrl,
          },
          logprobs: null,
        }
      ],
      usage: {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0
      }
    };
    res.json(chatResp);
  } catch (err) {
    // 打印详细错误日志
    if (err.response) {
      console.error('Gitee API 调用异常:');
      console.error('status:', err.response.status);
      console.error('headers:', err.response.headers);
      console.error('data:', err.response.data);
      console.error('完整错误对象:', err);
      res.status(err.response.status || 500).json({
        error: 'Gitee API 调用失败',
        status: err.response.status,
        headers: err.response.headers,
        data: err.response.data,
        message: err.message
      });
    } else {
      // 其他异常
      console.error('未知异常:', err);
      res.status(500).json({ error: err.message });
    }
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`中转服务已启动，端口: ${PORT}`);
});
