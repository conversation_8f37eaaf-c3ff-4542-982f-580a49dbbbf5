# 计划：构建 AI 接口中转服务

## 笔记

- **目标**: 创建一个 Node.js 服务，作为客户端和 Gitee AI 平台之间的中转代理。
- **核心功能**: 将 Chat Completions 格式的请求转换为 Gitee 图像抠图 (Image Matting) API 的请求，然后将抠图 API 的响应再转换回 Chat Completions 格式返回给客户端。
- **技术栈**: Node.js, Express.js (或类似的框架), Axios (用于 HTTP 请求)。
- **性能要求**: 需要支持高并发，因此必须使用异步、非阻塞的编程模型。
- **数据流**: 
  1. 客户端发送 Chat Completions 格式的 POST 请求到我们的服务。
  2. 服务解析请求，从中提取图片数据（假设为 Base64 编码）和 API Key。
  3. 服务构建一个 `multipart/form-data` 请求，并调用 Gitee 的 `/v1/images/mattings` 接口。
  4. 服务接收到 Gitee 的响应（包含处理后图片的 URL）。
  5. 服务构建一个 Chat Completions 格式的响应，并将图片 URL 放入其中。
  6. 服务将最终响应发送回客户端。

## 任务列表

- [x] **1. 项目初始化**
  - [x] 初始化一个新的 Node.js 项目 (`package.json`)。
  - [x] 创建项目基本目录结构和主文件 `index.js`。
  - [x] 安装所需依赖：`express` (Web 框架), `axios` (HTTP 客户端), `form-data` (处理 multipart/form-data)。
- [ ] **2. 创建基础 Web 服务器**
  - [ ] 使用 Express 创建一个 HTTP 服务器。
  - [ ] 设置一个 POST 端点，例如 `/v1/chat/completions`，用于接收请求。
  - [ ] 确保服务器能成功启动并监听指定端口。
- [ ] **3. 实现请求转换和代理逻辑**
  - [ ] 在 POST 端点中，解析传入的 JSON 请求体。
  - [ ] 从请求体 (`messages[0].content`) 中提取 Base64 格式的图片数据。
  - [ ] 从请求头 (`Authorization`) 中获取 Gitee API Key。
  - [ ] 使用 `axios` 和 `form-data` 库构建并发送请求到 Gitee 的抠图 API。
- [ ] **4. 实现响应转换逻辑**
  - [ ] 处理来自 Gitee API 的响应。
  - [ ] 根据 Gitee API 返回的数据，构建一个符合 Chat Completions 格式的 JSON 响应。
  - [ ] 将构建好的响应发送回原始客户端。
- [ ] **5. 完善与部署**
  - [ ] 添加全面的错误处理机制（如 API 调用失败、无效输入等）。
  - [ ] 添加日志记录功能，方便调试和监控。
  - [ ] 编写 `README.md` 文件，说明如何安装、配置和运行该服务。

## 当前目标

设置 Node.js 项目并创建基础的 Express 服务器框架。